self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"78b0a7b4621215d3e009bad615aa0c8e291b9a408c\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\"\n      }\n    },\n    \"00d8ea5eacd67889d716c6e94cb523be8489f30102\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\"\n      }\n    },\n    \"407ec8c0baf10f4af661be90c6be44c7c4d8687c6e\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\"\n      }\n    },\n    \"4081367950493419f456f11c6940f3b5d761eaa0b0\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\"\n      }\n    },\n    \"40004164fa8b194866fc75411be78e64878ebfcfbf\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\"\n      }\n    },\n    \"608dde77d8a89d0de5d5614156559b51975b530140\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\"\n      }\n    },\n    \"6075444a4772c4d3ba6ee1bc9b99be09d67ddb0ecb\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\"\n      }\n    },\n    \"603877eee4547ee74ee81b280281834210f8401215\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\"\n      }\n    },\n    \"60ce54d499b3094b7f3fa176eb294c927e8f71cb32\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\"\n      }\n    },\n    \"60777dea14a1f629d41afe512d64154b9b37b79d76\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\"\n      }\n    },\n    \"40c529d0bfe7caa266dcfd889dbd8472224d3a6ac8\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\"\n      }\n    },\n    \"40af2f468b8fc2d94b290185629c86b464e8621dbd\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\"\n      }\n    },\n    \"602ee9299bfb41efd01345bd9ac385b620f23bec33\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\"\n      }\n    },\n    \"7c2971568d55ff35cebc3c0d9c6874df175b4aa36a\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\"\n      }\n    },\n    \"00412a508979cdfc33a76327d0d3c2618c61d67117\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\"\n      }\n    },\n    \"40746252270b5a469b29c1561cda6e5121e47c3955\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\"\n      }\n    },\n    \"60958e9af4426b3e30e79c3f73a87dfce973ac15c6\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\"\n      }\n    },\n    \"60e051bfa38aa926899bae0205705474b165e0d83b\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\"\n      }\n    },\n    \"7094c2b983d25368339607c147c0682cc384744563\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\"\n      }\n    },\n    \"60c0a645dde7d659fb835f793e0b9553ec4a3a26ac\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\"\n      }\n    },\n    \"60038f48330a13767eedb69b43c890d57074ec2dc1\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\"\n      }\n    },\n    \"702722b1badbc2c5b19af3a06b9386c3ed60c89298\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\"\n      }\n    },\n    \"60ffa4129c53cfa64fc774671de1d2905981e7200a\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\"\n      }\n    },\n    \"60a260147a92007b5f196c85b554d90a177b0d0b28\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\"\n      }\n    },\n    \"70cdf7078ce728bc8962538f288ee6ae072cf0f2d8\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\"\n      }\n    },\n    \"60b29ae6161547cfcc6b1ddbddb71e1f62150661c6\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"fiJYhC2LsNXReKgU6J84dMuJnJYut0OE5D/HYEThQ3w=\"\n}"